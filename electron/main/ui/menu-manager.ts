import type { BrowserWindow, MenuItemConstructorOptions } from 'electron'
import { Menu, app, dialog, shell } from 'electron'
import { moduleErrorHandlers } from '../core/error-handler'
import { UpdateService } from '../services/update-service'
import { UpdateStatus } from '../../types/update-service'

/**
 * 菜单管理器
 * 负责创建和管理应用程序的各种菜单
 */
export class MenuManager {
  private contextMenu: Menu | null = null
  private dockMenu: Menu | null = null
  private applicationMenu: Menu | null = null
  /**
   * 创建右键上下文菜单
   */
  createContextMenu(window: BrowserWindow): void {
    try {
      const template: MenuItemConstructorOptions[] = [
        {
          label: '复制',
          role: 'copy',
        },
        {
          label: '粘贴',
          role: 'paste',
        },
        {
          label: '剪切',
          role: 'cut',
        },
        {
          type: 'separator',
        },
        {
          label: '全选',
          role: 'selectAll',
        },
        {
          type: 'separator',
        },
        {
          label: '重新加载',
          role: 'reload',
        },
        {
          label: '强制重新加载',
          role: 'forceReload',
        },
        {
          label: '开发者工具',
          role: 'toggleDevTools',
        },

      ]

      this.contextMenu = Menu.buildFromTemplate(template)

      // 监听右键事件
      window.webContents.on('context-menu', (_event, _params) => {
        if (this.contextMenu) {
          this.contextMenu.popup({ window })
        }
      })

      moduleErrorHandlers.menu.info('右键上下文菜单创建成功', 'createContextMenu')
    }
    catch (error) {
      moduleErrorHandlers.menu.error(
        error instanceof Error ? error : String(error),
        'createContextMenu',
      )
    }
  }

  /**
   * 创建 Dock 菜单 (macOS)
   */
  createDockMenu(): void {
    if (process.platform !== 'darwin') {
      return
    }

    try {
      const template: MenuItemConstructorOptions[] = [
        {
          label: '新建窗口',
          click: () => {
            // 这里可以添加创建新窗口的逻辑
            moduleErrorHandlers.menu.info('从 Dock 菜单创建新窗口', 'createDockMenu')
          },
        },
        {
          type: 'separator',
        },
      ]

      this.dockMenu = Menu.buildFromTemplate(template)
      if (this.dockMenu && app.dock) {
        app.dock.setMenu(this.dockMenu)
      }

      moduleErrorHandlers.menu.info('Dock 菜单创建成功', 'createDockMenu')
    }
    catch (error) {
      moduleErrorHandlers.menu.error(
        error instanceof Error ? error : String(error),
        'createDockMenu',
      )
    }
  }

  /**
   * 创建应用程序菜单栏
   */
  createApplicationMenu(window: BrowserWindow): void {
    try {
      const template: MenuItemConstructorOptions[] = this.getApplicationMenuTemplate(window)
      this.applicationMenu = Menu.buildFromTemplate(template)
      Menu.setApplicationMenu(this.applicationMenu)

      moduleErrorHandlers.menu.info('应用程序菜单栏创建成功', 'createApplicationMenu')
    }
    catch (error) {
      moduleErrorHandlers.menu.error(
        error instanceof Error ? error : String(error),
        'createApplicationMenu',
      )
    }
  }

  /**
   * 获取应用程序菜单模板
   */
  private getApplicationMenuTemplate(window: BrowserWindow) {
    const isMac = process.platform === 'darwin'

    const template: MenuItemConstructorOptions[] = [
      // macOS 应用菜单
      ...(isMac
        ? [{
            label: app.getName(),
            submenu: [
              { role: 'about' as const, label: '关于' },
              {
                label: '检查更新',
                click: async () => {
                  moduleErrorHandlers.menu.info('检查更新功能被点击', 'applicationMenu')
                  await this.handleCheckForUpdates(window)
                },
              },
              {
                label: '访问官网',
                click: () => shell.openExternal('https://your-website.com'),
              },
              { type: 'separator' as const },
              { role: 'hide' as const, label: '隐藏' },
              { role: 'hideOthers' as const, label: '隐藏其他' },
              { role: 'unhide' as const, label: '显示全部' },
              { type: 'separator' as const },
              { role: 'quit' as const, label: '退出' },
            ],
          }]
        : []),

      // 编辑菜单
      {
        label: '编辑',
        submenu: [
          { role: 'undo' as const, label: '撤销' },
          { role: 'redo' as const, label: '重做' },
          { type: 'separator' as const },
          { role: 'cut' as const, label: '剪切' },
          { role: 'copy' as const, label: '复制' },
          { role: 'paste' as const, label: '粘贴' },
          ...(isMac
            ? [
                { role: 'pasteAndMatchStyle' as const, label: '粘贴并匹配样式' },
                { role: 'delete' as const, label: '删除' },
                { role: 'selectAll' as const, label: '全选' },
                { type: 'separator' as const },
                {
                  label: '语音',
                  submenu: [
                    { role: 'startSpeaking' as const, label: '开始朗读' },
                    { role: 'stopSpeaking' as const, label: '停止朗读' },
                  ],
                },
              ]
            : [
                { role: 'delete' as const, label: '删除' },
                { type: 'separator' as const },
                { role: 'selectAll' as const, label: '全选' },
              ]),
        ],
      },

      // 视图菜单
      {
        label: '查看',
        submenu: [
          { role: 'reload' as const, label: '刷新' },
          { type: 'separator' as const },
          { role: 'resetZoom' as const, label: '重置缩放' },
          { role: 'zoomIn' as const, label: '放大' },
          { role: 'zoomOut' as const, label: '缩小' },
          { type: 'separator' as const },
          { role: 'togglefullscreen' as const, label: '切换全屏' },
          { role: 'forceReload' as const, label: '强制重新加载' },
          { role: 'toggleDevTools' as const, label: '切换开发者工具' },
        ],
      },

      // 窗口菜单
      {
        label: '窗口',
        submenu: [
          { role: 'minimize' as const, label: '最小化' },
          { role: 'close' as const, label: '关闭' },
          {
            label: '居中',
            accelerator: 'CmdOrCtrl+Shift+C',
            click: () => window?.center(),
          },
          ...(isMac
            ? [
                { type: 'separator' as const },
                { role: 'front' as const, label: '前置所有窗口' },
              ]
            : []),
        ],
      },

      // 帮助菜单
      {
        label: '帮助',
        submenu: [
          {
            label: '打开日志',
            click: () => console.log('打开日志'),
          },
          {
            label: '清除缓存',
            click: () => window?.webContents.session.clearCache(),
          },
          {
            label: '官网',
            click: () => shell.openExternal('https://your-website.com'),
          },
          {
            label: '留言反馈',
            click: () => shell.openExternal('https://your-feedback-url.com'),
          },
        ],
      },
    ]

    return template
  }

  /**
   * 更新菜单项状态
   */
  updateMenuItemState(menuId: string, enabled: boolean): void {
    try {
      const menu = Menu.getApplicationMenu()

      if (menu) {
        const menuItem = menu.getMenuItemById(menuId)
        if (menuItem) {
          menuItem.enabled = enabled
        }
      }

      moduleErrorHandlers.menu.info(`菜单项 ${menuId} 状态更新为 ${enabled}`, 'updateMenuItemState')
    }
    catch (error) {
      moduleErrorHandlers.menu.error(
        error instanceof Error ? error : String(error),
        'updateMenuItemState',
      )
    }
  }

  /**
   * 处理检查更新
   */
  private async handleCheckForUpdates(window: BrowserWindow): Promise<void> {
    try {
      moduleErrorHandlers.menu.info('开始检查更新', 'handleCheckForUpdates')

      // 调用更新服务检查更新
      const checkResult = await UpdateService.checkForUpdates()
      console.log('检查更新结果:', checkResult)
      moduleErrorHandlers.menu.info('开始检查更新', 'handleCheckForUpdates', checkResult)

      if (!checkResult.success) {
        // 检查更新失败
        await dialog.showMessageBox(window, {
          type: 'error',
          title: '检查更新失败',
          message: '检查更新时发生错误',
          detail: checkResult.error || '未知错误',
          buttons: ['确定'],
        })
        return
      }

      // 等待一下让更新状态传播
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 获取更新信息
      const updateInfo = UpdateService.getUpdateInfo()
      const currentVersion = UpdateService.getCurrentVersion()

      if (updateInfo.status === UpdateStatus.AVAILABLE) {
        // 有可用更新
        const result = await dialog.showMessageBox(window, {
          type: 'info',
          title: '发现新版本',
          message: `发现新版本 ${updateInfo.version}`,
          detail: `当前版本：${currentVersion}\n新版本：${updateInfo.version}\n\n更新内容：\n${updateInfo.releaseNotes || '暂无更新说明'}`,
          buttons: ['立即下载', '稍后提醒'],
          defaultId: 0,
          cancelId: 1,
        })

        if (result.response === 0) {
          // 用户选择立即下载
          const downloadResult = await UpdateService.downloadUpdate()
          if (!downloadResult.success) {
            await dialog.showMessageBox(window, {
              type: 'error',
              title: '下载失败',
              message: '下载更新失败',
              detail: downloadResult.error || '未知错误',
              buttons: ['确定'],
            })
          }
        }
      }
      else {
        // 没有可用更新
        await dialog.showMessageBox(window, {
          type: 'info',
          title: '检查更新',
          message: '当前版本已经是最新的',
          detail: `当前版本：${currentVersion}\n您使用的已经是最新版本，无需更新。`,
          buttons: ['确定'],
        })
      }

      moduleErrorHandlers.menu.info('检查更新完成', 'handleCheckForUpdates')
    }
    catch (error) {
      moduleErrorHandlers.menu.error(
        error instanceof Error ? error : String(error),
        'handleCheckForUpdates',
      )

      // 显示错误对话框
      await dialog.showMessageBox(window, {
        type: 'error',
        title: '检查更新失败',
        message: '检查更新时发生错误',
        detail: error instanceof Error ? error.message : String(error),
        buttons: ['确定'],
      })
    }
  }

  /**
   * 清理菜单资源
   */
  cleanup(): void {
    try {
      this.contextMenu = null
      this.dockMenu = null
      this.applicationMenu = null
      moduleErrorHandlers.menu.info('菜单管理器清理完成', 'cleanup')
    }
    catch (error) {
      moduleErrorHandlers.menu.error(
        error instanceof Error ? error : String(error),
        'cleanup',
      )
    }
  }
}
