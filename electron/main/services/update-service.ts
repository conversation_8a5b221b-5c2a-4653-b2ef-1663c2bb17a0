import { app, dialog } from 'electron'
import pkg from 'electron-updater'
import type { BrowserWindow } from 'electron'
import type { UpdateInfo } from '../../types/update-service'
import { UpdateStatus } from '../../types/update-service'
import { mainIPC } from '../../ipc/main-ipc'
import { BaseService } from './base-service'
import type { ServiceResult } from './base-service'

const { autoUpdater } = pkg

/**
 * 更新服务类
 * 负责处理应用程序的自动更新功能
 */
export class UpdateService extends BaseService {
  protected static serviceName = 'UpdateService' // 服务名称，用于日志记录和标识
  private static mainWindow: BrowserWindow | null = null // 主窗口引用，用于向渲染进程发送更新状态
  private static isChecking = false // 标记当前是否正在检查更新
  private static isDownloading = false // 标记当前是否正在下载更新
  private static isInitialized = false
  private static updateInfo: UpdateInfo = { status: UpdateStatus.NOT_AVAILABLE } // 当前更新信息，初始状态为"无可用更新"

  /**
   * 初始化更新服务
   */
  static initialize(): void {
    if (this.isInitialized) {
      this.logWarning('UpdateService 已经初始化，跳过重复初始化', 'initialize')
      return
    }
    this.setupAutoUpdater()
    this.isInitialized = true
    this.logInfo('UpdateService 初始化完成', 'initialize')
  }

  /**
   * 设置主窗口引用
   */
  static setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window

    // 确保服务已初始化
    if (!this.isInitialized) {
      this.initialize()
    }

    // 延迟检查更新，给应用充分的启动时间
    setTimeout(() => {
      this.checkForUpdatesWithUI() // 带UI提示的检查
    }, 5 * 1000) // 5秒延迟

    this.logInfo('主窗口已设置，更新服务准备就绪', 'setMainWindow')
  }

  /**
   * 配置 autoUpdater
   */
  private static setupAutoUpdater(): void {
    // 配置更新服务器
    autoUpdater.setFeedURL({
      provider: 'generic',
      url: 'https://example.com/auto-updates', // 替换为您的更新服务器地址
      channel: 'latest',
    })

    // 根据平台配置更新策略
    if (process.platform === 'darwin') {
      // macOS 支持 ZIP 差分更新
      autoUpdater.allowDowngrade = false
      autoUpdater.allowPrerelease = false
      this.logInfo('macOS平台：支持ZIP差分更新', 'setupAutoUpdater')
    }
    else if (process.platform === 'win32') {
      // Windows 支持 NSIS 差分包更新
      autoUpdater.allowDowngrade = false
      autoUpdater.allowPrerelease = false
      this.logInfo('Windows平台：支持NSIS差分包更新', 'setupAutoUpdater')
    }
    else {
      // Linux 使用全量更新
      autoUpdater.allowDowngrade = false
      autoUpdater.allowPrerelease = false
      this.logInfo('Linux平台：使用全量更新', 'setupAutoUpdater')
    }

    // 配置更新选项
    autoUpdater.autoDownload = false // 手动控制下载
    autoUpdater.autoInstallOnAppQuit = true // 退出时自动安装
    autoUpdater.autoRunAppAfterInstall = true // 安装后自动启动

    // 配置日志
    autoUpdater.logger = {
      info: message => this.logInfo(message, 'autoUpdater'),
      warn: message => this.logWarning(message, 'autoUpdater'),
      error: message => this.logError(message, 'autoUpdater'),
      debug: message => this.logInfo(`[DEBUG] ${message}`, 'autoUpdater'),
    }

    this.setupEventListeners()
  }

  /**
   * 设置事件监听器
   */
  private static setupEventListeners(): void {
    // 检查更新开始
    autoUpdater.on('checking-for-update', () => {
      this.isChecking = true
      this.updateInfo = { status: UpdateStatus.CHECKING }
      this.notifyRenderer()
      this.logInfo('开始检查更新', 'setupEventListeners')
    })

    // 发现可用更新
    autoUpdater.on('update-available', (info) => {
      this.isChecking = false
      this.updateInfo = {
        status: UpdateStatus.AVAILABLE,
        version: info.version,
        releaseNotes: info.releaseNotes as string,
      }
      this.notifyRenderer()
      this.logInfo(`发现新版本: ${info.version}`, 'setupEventListeners')
    })

    // 没有可用更新
    autoUpdater.on('update-not-available', (info) => {
      this.isChecking = false
      this.updateInfo = {
        status: UpdateStatus.NOT_AVAILABLE,
        version: info.version,
      }
      this.notifyRenderer()
      this.logInfo('当前已是最新版本', 'setupEventListeners')
    })

    // 下载进度
    autoUpdater.on('download-progress', (progressObj) => {
      this.updateInfo = {
        status: UpdateStatus.DOWNLOADING,
        progress: {
          percent: progressObj.percent,
          bytesPerSecond: progressObj.bytesPerSecond,
          total: progressObj.total,
          transferred: progressObj.transferred,
        },
      }
      this.notifyRenderer()
    })

    // 下载完成
    autoUpdater.on('update-downloaded', (info) => {
      this.isDownloading = false
      this.updateInfo = {
        status: UpdateStatus.DOWNLOADED,
        version: info.version,
        releaseNotes: info.releaseNotes as string,
      }
      this.notifyRenderer()
      this.logInfo('更新下载完成', 'setupEventListeners')

      // 询问用户是否立即安装
      this.promptInstallUpdate()
    })

    // 更新错误
    autoUpdater.on('error', (error) => {
      this.isChecking = false
      this.isDownloading = false
      this.updateInfo = {
        status: UpdateStatus.ERROR,
        error: error.message,
      }
      this.notifyRenderer()
      this.logError(`更新错误: ${error.message}`, 'setupEventListeners')
    })
  }

  /**
   * 检查更新
   */
  static async checkForUpdates(): Promise<ServiceResult<void>> {
    // 确保服务已初始化
    if (!this.isInitialized) {
      this.initialize()
    }
    return this.safeExecute(
      async () => {
        if (this.isChecking) {
          this.logWarning('正在检查更新中，请稍候', 'checkForUpdates')
          return
        }

        await autoUpdater.checkForUpdatesAndNotify()
      },
      'checkForUpdates',
      '检查更新失败',
    )
  }

  /**
   * 带UI提示的检查更新（用于应用启动时自动检查）
   */
  static async checkForUpdatesWithUI(): Promise<void> {
    try {
      // 确保服务已初始化
      if (!this.isInitialized) {
        this.initialize()
      }

      if (!this.mainWindow) {
        this.logWarning('主窗口未设置，跳过UI检查更新', 'checkForUpdatesWithUI')
        return
      }

      this.logInfo('开始自动检查更新', 'checkForUpdatesWithUI')

      // 调用检查更新
      const checkResult = await this.checkForUpdates()

      if (!checkResult.success) {
        this.logError(`自动检查更新失败: ${checkResult.error}`, 'checkForUpdatesWithUI')
        return
      }

      // 等待一下让更新状态传播
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 获取更新信息
      const updateInfo = this.getUpdateInfo()
      const currentVersion = this.getCurrentVersion()

      if (updateInfo.status === UpdateStatus.AVAILABLE) {
        // 有可用更新，显示提示对话框
        const result = await dialog.showMessageBox(this.mainWindow, {
          type: 'info',
          title: '发现新版本',
          message: `发现新版本 ${updateInfo.version}`,
          detail: `当前版本：${currentVersion}\n新版本：${updateInfo.version}\n\n更新内容：\n${updateInfo.releaseNotes || '暂无更新说明'}`,
          buttons: ['立即下载', '稍后提醒'],
          defaultId: 0,
          cancelId: 1,
        })

        if (result.response === 0) {
          // 用户选择立即下载
          const downloadResult = await this.downloadUpdate()
          if (!downloadResult.success) {
            await dialog.showMessageBox(this.mainWindow, {
              type: 'error',
              title: '下载失败',
              message: '下载更新失败',
              detail: downloadResult.error || '未知错误',
              buttons: ['确定'],
            })
          }
        }

        this.logInfo('自动检查更新完成，发现新版本', 'checkForUpdatesWithUI')
      }
      else {
        // 没有可用更新，静默处理，不显示对话框
        this.logInfo('自动检查更新完成，当前已是最新版本', 'checkForUpdatesWithUI')
      }
    }
    catch (error) {
      this.logError(
        error instanceof Error ? error : String(error),
        'checkForUpdatesWithUI',
      )
    }
  }

  /**
   * 下载更新
   */
  static async downloadUpdate(): Promise<ServiceResult<void>> {
    return this.safeExecute(
      async () => {
        if (this.isDownloading) {
          this.logWarning('正在下载更新中', 'downloadUpdate')
          return
        }

        if (this.updateInfo.status !== UpdateStatus.AVAILABLE) {
          this.logWarning('没有可用的更新', 'downloadUpdate')
          return
        }

        this.isDownloading = true
        await autoUpdater.downloadUpdate()
      },
      'downloadUpdate',
      '下载更新失败',
    )
  }

  /**
   * 安装更新并重启应用
   */
  static installUpdate(): ServiceResult<void> {
    try {
      if (this.updateInfo.status !== UpdateStatus.DOWNLOADED) {
        this.logWarning('更新尚未下载完成', 'installUpdate')
        return this.createErrorResult('更新尚未下载完成')
      }

      this.updateInfo.status = UpdateStatus.INSTALLING
      this.notifyRenderer()

      this.logInfo('开始安装更新并重启应用', 'installUpdate')
      autoUpdater.quitAndInstall()

      return this.createSuccessResult()
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'installUpdate')
      return this.createErrorResult(error instanceof Error ? error.message : '安装更新失败')
    }
  }

  /**
   * 提示用户安装更新
   */
  private static async promptInstallUpdate(): Promise<void> {
    if (!this.mainWindow)
      return

    const result = await dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: '更新下载完成',
      message: '新版本已下载完成，是否立即重启应用进行安装？',
      detail: '您可以选择稍后手动重启应用来完成更新。',
      buttons: ['立即重启', '稍后重启'],
      defaultId: 0,
      cancelId: 1,
    })

    if (result.response === 0) {
      this.installUpdate()
    }
  }

  /**
   * 通知渲染进程更新状态
   */
  private static notifyRenderer(): void {
    try {
      // 使用 mainIPC 发送事件到所有渲染进程
      mainIPC.emit('update-status', this.updateInfo)
      this.logInfo('更新状态已通知到渲染进程', 'notifyRenderer')
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'notifyRenderer')
    }
  }

  /**
   * 获取当前更新信息
   */
  static getUpdateInfo(): UpdateInfo {
    return { ...this.updateInfo }
  }

  /**
   * 获取当前应用版本
   */
  static getCurrentVersion(): string {
    return app.getVersion()
  }

  /**
   * 清理资源
   */
  static cleanup(): void {
    autoUpdater.removeAllListeners()
    this.mainWindow = null
    this.logInfo('UpdateService 资源清理完成', 'cleanup')
  }
}
